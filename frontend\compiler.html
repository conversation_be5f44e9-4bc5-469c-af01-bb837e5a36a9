<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechLearn - Code Editor</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Monaco Editor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.36.1/min/vs/loader.min.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles/style.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navbar -->
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.html" class="text-2xl font-bold text-indigo-600">TechLearn</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="backBtn" class="text-gray-600 hover:text-indigo-600">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Left Column: Exercise Info and Code Editor -->
            <div class="space-y-6">
                <!-- Exercise Info -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h2 id="exerciseTitle" class="text-2xl font-bold text-gray-900 mb-4">Loading...</h2>
                    <div id="exerciseDescription" class="prose max-w-none text-gray-600">
                        Loading exercise description...
                    </div>
                </div>

                <!-- Code Editor -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Code Editor</h3>
                        <div class="flex space-x-2">
                            <button id="runBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                                <i class="fas fa-play mr-2"></i>Run Code
                            </button>
                            <button id="resetBtn" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200">
                                <i class="fas fa-undo mr-2"></i>Reset
                            </button>
                        </div>
                    </div>
                    <div id="editor" class="h-[500px] border rounded-lg"></div>
                </div>
            </div>

            <!-- Right Column: Preview and Output -->
            <div class="space-y-6">
                <!-- Preview -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                    <div id="preview" class="border rounded-lg p-4 min-h-[200px] bg-white">
                        <iframe id="previewFrame" class="w-full h-[400px] border-0"></iframe>
                    </div>
                </div>

                <!-- Console Output -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Console Output</h3>
                    <div id="console" class="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm h-[200px] overflow-y-auto">
                        <div class="text-gray-400">Console output will appear here...</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="scripts/compiler.js"></script>
</body>
</html> 