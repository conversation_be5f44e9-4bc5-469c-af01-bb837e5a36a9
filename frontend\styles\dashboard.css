* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f0f23 75%, #0e1117 100%);
    color: #ffffff;
    min-height: 100vh;
    scroll-behavior: smooth;
    position: relative;
}

/* Background Pattern */
.bg-pattern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background:
        radial-gradient(ellipse at top left, rgba(55, 65, 81, 0.1) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(31, 41, 55, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at center, rgba(17, 24, 39, 0.2) 0%, transparent 70%);
}

.bg-dots {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23374151' fill-opacity='0.08'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.6;
}

/* Header Styles */
.header {
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-bottom: 1px solid rgba(75, 85, 99, 0.3);
    position: sticky;
    top: 0;
    z-index: 50;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo {
    background: #d9d9d9;
    color: #070054;
    padding: 8px 16px;
    border-radius: 16px;
    font-weight: bold;
    font-size: 18px;
}

.nav {
    display: flex;
    gap: 2px;
    align-items: center;
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 50px;
    padding: 6px;
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.nav-item {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
}

.nav-item.active {
    background: #2563EB;
    color: white;
    font-weight: 600;
    box-shadow:
        0 2px 8px rgba(37, 99, 235, 0.3),
        0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.nav-item:hover:not(.active) {
    background: rgba(156, 163, 175, 0.15);
    color: #f3f4f6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Settings button with text */
.settings-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 50px;
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(75, 85, 99, 0.3);
    color: #9ca3af;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.settings-btn:hover {
    background: rgba(156, 163, 175, 0.2);
    color: #f3f4f6;
}

.settings-btn i {
    font-size: 14px;
}

/* Single icon buttons */
.icon-btn-single {
    width: 40px;
    height: 40px;
    border-radius: 50px;
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(75, 85, 99, 0.3);
    color: #9ca3af;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 16px;
}

.icon-btn-single:hover {
    background: rgba(156, 163, 175, 0.2);
    color: #f3f4f6;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50px;
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(75, 85, 99, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.avatar:hover {
    background: rgba(156, 163, 175, 0.2);
    color: #f3f4f6;
}

/* Profile container and dropdown */
.profile-container {
    position: relative;
}

.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: rgba(31, 41, 55, 0.9);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 12px;
    padding: 8px;
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.profile-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.logout-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border: none;
    background: none;
    color: #9ca3af;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

/* Main Content */
.main {
    position: relative;
    padding: 32px 24px;
    z-index: 10;
}

/* Welcome Section Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: #3b82f6;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}



@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
    }
}

@keyframes progressFill {
    from {
        width: 0%;
    }
    to {
        width: var(--target-width);
    }
}

.welcome-section {
    margin-bottom: 48px;
    padding: 32px 0;
}

.welcome-greeting {
    text-align: center;
    margin-bottom: 32px;
}

.welcome-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    position: relative;
}

#typing-text {
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    border-right: 3px solid #3b82f6;
    animation: typing 2s steps(20, end), blink 1s infinite;
    animation-delay: 0s, 2s;
}

.static-emoji {
    opacity: 0;
    animation: fadeIn 0.3s ease-in 2s both;
}



.user-name {
    color: #3b82f6;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.time-greeting {
    font-size: 1.25rem;
    color: #94a3b8;
    font-weight: 500;
    animation: fadeInRight 0.8s ease-out 2.5s both;
    margin: 0;
    opacity: 0;
}

.welcome-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    max-width: 800px;
    margin: 0 auto;
}

.stat-chip {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(31, 41, 55, 0.6);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(75, 85, 99, 0.3);
    border-radius: 50px;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: fadeInScale 0.6s ease-out both;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-chip::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.stat-chip:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
}

.stat-chip:hover::before {
    left: 100%;
}

.stat-icon {
    font-size: 18px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.stat-text {
    color: #e2e8f0;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
}

/* Responsive Welcome Section */
@media (max-width: 768px) {
    .welcome-title {
        font-size: 2.25rem;
    }

    .time-greeting {
        font-size: 1.125rem;
    }

    .welcome-stats {
        flex-direction: column;
        align-items: center;
        gap: 16px;
    }

    .stat-chip {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: 1.875rem;
    }

    .welcome-section {
        padding: 24px 0;
        margin-bottom: 32px;
    }

    .stat-text {
        font-size: 13px;
    }
}

/* Grid Layout */
.top-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 24px;
    margin-bottom: 32px;
}

.progress-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}

/* Card Styles */
.card {
    background: rgba(31, 41, 55, 0.4);
    border: 1px solid rgba(75, 85, 99, 0.3);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 20px;
    padding: 24px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: cardSlideIn 0.6s ease-out both;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
}

.card:hover::before {
    opacity: 1;
}

/* Staggered card animations */
.top-grid .card:nth-child(1) {
    animation-delay: 0.2s;
}

.progress-grid .card:nth-child(1) {
    animation-delay: 0.4s;
}

.progress-grid .card:nth-child(2) {
    animation-delay: 0.6s;
}

.progress-grid .card:nth-child(3) {
    animation-delay: 0.8s;
}

.profile-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.profile-avatar {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    background: linear-gradient(to bottom right, #2563eb, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
    border: 4px solid rgba(59, 130, 246, 0.2);
}

.profile-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 4px;
}

.profile-role {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 16px;
}

.profile-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: center;
}

.skill-badge {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* Progress Cards */
.card-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 16px;
}

.card-title {
    font-weight: 600;
    flex: 1;
}

.card-icon {
    width: 20px;
    height: 20px;
    color: #3b82f6;
}

.progress-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 4px;
}

.progress-label {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 16px;
}

.chart-bars {
    display: flex;
    align-items: flex-end;
    gap: 4px;
    height: 64px;
    margin-bottom: 8px;
}

.chart-bar {
    width: 8px;
    background: #374151;
    border-radius: 4px 4px 0 0;
}

.chart-bar.active {
    background: #f59e0b;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6b7280;
}

/* Exercise Progress Styles */
.exercise-progress-bar {
    width: 100%;
    height: 8px;
    background: #374151;
    border-radius: 4px;
    margin: 16px 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    --target-width: 40%;
    animation: progressFill 1.5s ease-out 1s both;
}

.exercise-breakdown {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 12px;
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.breakdown-icon {
    font-size: 14px;
}

.breakdown-text.completed {
    color: #10b981;
}

.breakdown-text.partial {
    color: #f59e0b;
}

.breakdown-text.pending {
    color: #94a3b8;
}

/* Timer Card */
.timer-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.timer-circle {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 4px solid #374151;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timer-progress {
    position: absolute;
    top: -4px;
    left: -4px;
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 4px solid #f59e0b;
    border-top-color: transparent;
    transform: rotate(45deg);
}

.timer-content {
    text-align: center;
}

.timer-time {
    font-size: 20px;
    font-weight: bold;
}

.timer-label {
    font-size: 12px;
    color: #94a3b8;
}

.timer-controls {
    display: flex;
    justify-content: center;
}

.play-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #334155;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.play-btn:hover {
    background: #475569;
}

/* Continue Learning Styles */
.continue-learning-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.current-exercise {
    text-align: center;
}

.exercise-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: white;
}

.exercise-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
    margin-bottom: 16px;
}

.exercise-status.incomplete {
    color: #f59e0b;
}

.status-icon {
    font-size: 14px;
}

.resume-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 10px 16px;
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.resume-btn:hover {
    background: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.pulse-btn {
    animation: pulse 2s infinite;
}

.pulse-btn:hover {
    animation: none;
}

/* Onboarding Card */
.task-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-check {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-check.completed {
    background: #10b981;
    color: white;
}

.task-check.pending {
    border: 2px solid #6b7280;
}

.task-text {
    font-size: 14px;
}

.task-text.completed {
    color: white;
}

.task-text.pending {
    color: #94a3b8;
}

.onboarding-progress {
    text-align: right;
}

.progress-badge {
    background: #334155;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 14px;
}

/* Learning Goals Styles */
.goals-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.goal-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.goal-check {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.goal-check.completed {
    background: #10b981;
    color: white;
}

.goal-check.partial {
    background: #f59e0b;
    color: white;
}

.goal-check.pending {
    border: 2px solid #6b7280;
}

.goal-text {
    font-size: 14px;
}

.goal-text.completed {
    color: #10b981;
}

.goal-text.partial {
    color: #f59e0b;
}

.goal-text.pending {
    color: #94a3b8;
}

.goals-progress {
    margin-top: 16px;
}

.goals-progress-text {
    font-size: 12px;
    color: #94a3b8;
    margin-bottom: 8px;
}

.goals-progress-bar {
    width: 100%;
    height: 6px;
    background: #374151;
    border-radius: 3px;
    overflow: hidden;
}

.goals-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #f59e0b, #d97706);
    border-radius: 3px;
    --target-width: 30%;
    animation: progressFill 1.5s ease-out 1.2s both;
}

/* Stats Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 8px;
}

.stat-label {
    color: #94a3b8;
    font-size: 14px;
}

/* Exercises Section */
.exercises-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.exercises-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

.exercise-card {
    background: rgba(31, 41, 55, 0.4);
    border: 1px solid rgba(75, 85, 99, 0.3);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 20px;
    padding: 24px;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.exercise-card:hover {
    background: rgba(31, 41, 55, 0.6);
    transform: translateY(-4px) scale(1.01);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(59, 130, 246, 0.4);
}

.exercise-card.completed {
    border-color: rgba(16, 185, 129, 0.3);
}

.exercise-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.tech-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    border: 1px solid;
}

.tech-html {
    background: rgba(251, 146, 60, 0.1);
    color: #fb923c;
    border-color: rgba(251, 146, 60, 0.2);
}

.tech-css {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border-color: rgba(59, 130, 246, 0.2);
}

.tech-js {
    background: rgba(234, 179, 8, 0.1);
    color: #eab308;
    border-color: rgba(234, 179, 8, 0.2);
}

.tech-fullstack {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
    border-color: rgba(168, 85, 247, 0.2);
}

.difficulty-beginner {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border-color: rgba(16, 185, 129, 0.2);
}

.difficulty-intermediate {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.2);
}

.difficulty-advanced {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.2);
}

.completed-badge {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    margin-bottom: 8px;
    display: inline-block;
}

.exercise-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
}

.exercise-description {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.5;
}

.exercise-btn {
    width: 100%;
    padding: 12px;
    border-radius: 24px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.exercise-btn.start {
    background: #070054;
    color: white;
    box-shadow: 0 4px 6px rgba(7, 0, 84, 0.3);
}

.exercise-btn.start:hover {
    background: rgba(7, 0, 84, 0.8);
    transform: scale(1.02);
    box-shadow: 0 6px 12px rgba(7, 0, 84, 0.4);
}

.exercise-btn.review {
    background: #059669;
    color: white;
    box-shadow: 0 4px 6px rgba(5, 150, 105, 0.2);
}

.exercise-btn.review:hover {
    background: #047857;
    transform: scale(1.02);
}

/* Calendar Section */
.calendar-section {
    margin-bottom: 32px;
}

.calendar-header {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 24px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.calendar-day {
    text-align: center;
}

.day-label {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 8px;
}

.day-number {
    font-weight: 500;
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.event-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: rgba(31, 41, 55, 0.4);
    border: 1px solid rgba(75, 85, 99, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 16px;
}

.event-info h4 {
    font-weight: 500;
    margin-bottom: 4px;
}

.event-info p {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 2px;
}

.event-info small {
    color: #6b7280;
    font-size: 12px;
}

.event-avatars {
    display: flex;
    gap: -8px;
}

.event-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #0f172a;
    margin-left: -8px;
}

.event-avatar:first-child {
    margin-left: 0;
}

.avatar-blue { background: #3b82f6; }
.avatar-green { background: #10b981; }
.avatar-yellow { background: #f59e0b; }
.avatar-purple { background: #8b5cf6; }
.avatar-red { background: #ef4444; }

/* Footer */
.footer {
    background: #070054;
    color: white;
    padding: 48px 24px 24px;
    position: relative;
    z-index: 10;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 32px;
    margin-bottom: 24px;
}

.footer-brand {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.footer-logo {
    width: 40px;
    height: 40px;
    background: #d9d9d9;
    margin-right: 12px;
    border-radius: 8px;
}

.footer-title {
    font-weight: bold;
    font-size: 20px;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 24px;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-link {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: background 0.2s;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.3);
}

.footer-section h4 {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 16px;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.footer-bottom-links {
    display: flex;
    gap: 24px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.footer-bottom-links a:hover {
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .top-grid {
        grid-template-columns: 1fr;
    }

    .progress-grid {
        grid-template-columns: 1fr;
    }

    .stats-section {
        grid-template-columns: 1fr;
    }

    .exercises-grid {
        grid-template-columns: 1fr;
    }

    .calendar-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        text-align: center;
        flex-direction: column;
    }

    .stats-row {
        flex-direction: column;
        gap: 12px;
    }
}