# Flask Authentication System 🔒

A secure user authentication system built with Flask, featuring user registration, login, and session management using bcrypt password hashing and MySQL.

![Python](https://img.shields.io/badge/python-3.6%2B-blue)
![Flask](https://img.shields.io/badge/flask-2.0%2B-green)
![License](https://img.shields.io/badge/license-MIT-orange)
![Build](https://img.shields.io/badge/build-passing-brightgreen)

## Features ✨
- User registration with email validation
- Secure password hashing using bcrypt
- Login/logout functionality
- Session management with Flask sessions
- Form validation (email, password complexity, mobile number)
- MySQL database integration

## Prerequisites 📋
- Python 3.6+
- MySQL Server
- Git (optional)
- Code editor (VS Code, PyCharm, etc.)

---

## Installation & Setup 🛠️

### 1. Clone the Repository
```bash
git clone https://github.com/YOUR_USERNAME/flask-auth-system.git
cd flask-auth-system
