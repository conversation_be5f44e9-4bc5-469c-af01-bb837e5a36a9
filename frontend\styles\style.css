/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --tech-blue: #3b82f6;
    --tech-grey: #9ca3af; /* From provided code */
    --primary-color: var(--tech-blue);
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--gray-900); /* Default text color - though most text is white on dark card */
    /* Background gradient from provided React code */
    background: linear-gradient(to bottom right, var(--gray-900), var(--tech-blue), var(--gray-800));
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    position: relative;
    overflow: hidden;
    scroll-behavior: smooth;
}

/* Coding-themed background elements */
.background-code {
    position: absolute;
    inset: 0;
    opacity: 0.10; /* Equivalent to opacity-10 */
    color: var(--tech-grey); /* Text color from provided code */
    font-family: 'Roboto Mono', monospace; /* Ensure monospace font is used */
}

.background-code div {
    position: absolute;
    white-space: nowrap;
}

/* Specific positions and sizes from provided code */
.background-code .code-element-1 { top: 40px; left: 40px; font-size: 60px; }
.background-code .code-element-2 { top: 80px; right: 80px; font-size: 40px; }
.background-code .code-element-3 { bottom: 80px; left: 80px; font-size: 50px; }
.background-code .code-element-4 { bottom: 40px; right: 40px; font-size: 30px; }
.background-code .code-element-5 { top: 50%; left: 25%; font-size: 40px; transform: translate(-50%, -50%); }
.background-code .code-element-6 { top: 33.333%; right: 33.333%; font-size: 30px; }

/* Animated floating code elements */
.animated-dots {
    position: absolute;
    inset: 0;
    overflow: hidden;
}

.animated-dots .dot {
    position: absolute;
    border-radius: 9999px; /* rounded-full */
    background-color: var(--tech-grey); /* Default color */
    opacity: 0.3; /* Default opacity */
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animated-dots .dot-1 { top: 25%; left: 25%; width: 8px; height: 8px; }
.animated-dots .dot-2 { top: 75%; right: 25%; width: 4px; height: 4px; background-color: white; opacity: 0.4; animation-delay: 1s; }
.animated-dots .dot-3 { top: 50%; left: 75%; width: 12px; height: 12px; opacity: 0.2; animation-delay: 0.5s; }

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* TechLearn Logo section */
.techlearn-logo {
    position: absolute;
    top: 32px;
    left: 32px;
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 20; /* Ensure it's above other background elements and card */
}

.techlearn-logo img {
    height: 40px;
    width: auto;
}

.techlearn-logo span {
    font-size: 20px;
    font-weight: bold;
    color: white; /* Color from provided code */
}

/* Auth Container Styles (now applied to the Card) */
.auth-card {
    width: 100%;
    max-width: 448px; /* Equivalent to max-w-md */
    padding: 32px;
    box-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);
    background: rgba(31, 41, 55, 0.9); /* Equivalent to bg-gray-800/90 */
    backdrop-filter: blur(4px);
    border-radius: 12px; /* Equivalent to rounded-xl */
    border: 1px solid var(--gray-700); /* Equivalent to border border-gray-700 */
    /* Add animation */
    opacity: 0;
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

.auth-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
}

.auth-icon .icon-wrapper {
    width: 64px;
    height: 64px;
    background-color: var(--gray-700); /* Equivalent to bg-gray-700 */
    border-radius: 12px; /* Equivalent to rounded-2xl */
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-icon i { /* Styling for Font Awesome icon */
     width: 32px;
    height: 32px;
    color: var(--tech-grey); /* Equivalent to text-tech-grey */
    font-size: 32px; /* Ensure icon scales */
}

.auth-title {
    text-align: center;
    margin-bottom: 32px;
}

.auth-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: white; /* Equivalent to text-white */
    margin-bottom: 8px;
}

.auth-title p {
    color: var(--gray-400); /* Equivalent to text-gray-400 */
    font-size: 16px;
}

.auth-form {
     display: flex;
     flex-direction: column;
     gap: 16px;
}

.form-group {
    position: relative;
}

.form-group i { /* Styling for Font Awesome icons in inputs */
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500); /* Equivalent to text-gray-500 */
    width: 20px;
    height: 20px;
    font-size: 20px; /* Ensure icon scales */
    z-index: 1; /* Ensure icon is above input */
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px 12px 40px; /* Adjust padding for icon, pl-10 */
    height: 48px;
    background-color: var(--gray-700); /* Equivalent to bg-gray-700 */
    border: 1px solid var(--gray-600); /* Equivalent to border-gray-600 */
    color: white; /* Equivalent to text-white */
    border-radius: 12px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input::placeholder { /* Styling for placeholder text */
    color: var(--gray-400); /* Equivalent to placeholder:text-gray-400 */
    opacity: 1; /* Ensure placeholder is visible */
}

.form-group input:focus {
    border-color: var(--tech-grey); /* Equivalent to focus:border-tech-grey */
    box-shadow: 0 0 0 1px var(--tech-grey); /* Simple focus ring */
}

.form-group select {
     padding-right: 16px; /* Ensure padding on the right for select */
     appearance: none; /* Remove default arrow */
     background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 viewBox%3D%220 0 20 20%22 fill%3D%22none%22 stroke%3D%22%239ca3af%22 stroke-width%3D%222%22 stroke-linecap%3D%22round%22 stroke-linejoin%3D%22round%22%3E%3Cpath d%3D%22M6 8l4 4 4-4%22%2F%3E%3C%2Fsvg%3E'); /* Custom arrow */
     background-repeat: no-repeat;
     background-position: right 12px top 50%;
     background-size: 1em auto;
}

.form-group .password-toggle {
     position: absolute;
     right: 12px;
     top: 50%;
     transform: translateY(-50%);
     background: none;
     border: none;
     cursor: pointer;
     padding: 0;
     color: var(--gray-500); /* Equivalent to text-gray-500 */
     transition: color 0.2s;
     z-index: 1; /* Ensure button is above input */
}

.form-group .password-toggle:hover {
    color: var(--gray-300); /* Equivalent to hover:text-gray-300 */
}

/* Forgot password link */
.forgot-password {
    display: flex;
    justify-content: flex-end;
    font-size: 14px;
    margin-top: -8px;
}

.forgot-password a {
    color: var(--tech-grey); /* Equivalent to text-tech-grey */
    text-decoration: none;
    transition: color 0.2s;
}

.forgot-password a:hover {
    color: white; /* Equivalent to hover:text-white */
    text-decoration: underline;
}

/* Button Styles (Primary) */
.btn-primary {
    width: 100%;
    height: 48px;
    background-color: var(--tech-blue); /* Equivalent to bg-tech-blue */
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s, shadow 0.3s; /* Added shadow transition */
    /* Added hover shadow from provided code */
}

.btn-primary:hover {
    background-color: #3b82f6cc; /* Equivalent to hover:bg-tech-blue/80 */
     box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* Equivalent to hover:shadow-lg */
}

/* Separator */
.separator {
    margin-top: 24px;
}

.separator .relative-container {
    position: relative;
}

.separator .absolute-container {
    position: absolute;
    inset: 0;
    display: flex;
    align-items: center;
}

.separator .border-t {
    width: 100%;
    border-top: 1px solid var(--gray-600); /* Equivalent to border-t border-gray-600 */
}

.separator .text-container {
    position: relative;
    display: flex;
    justify-content: center;
    font-size: 14px;
}

.separator span {
    padding-left: 16px;
    padding-right: 16px;
    background-color: var(--gray-800); /* Equivalent to bg-gray-800 */
    color: var(--gray-400); /* Equivalent to text-gray-400 */
}

/* Social Icons */
.social-icons {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    gap: 16px;
}

.social-icons button {
    width: 48px;
    height: 48px;
    border-radius: 9999px;
    background-color: var(--gray-700); /* Equivalent to bg-gray-700 */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    border: 1px solid var(--gray-600); /* Equivalent to border border-gray-600 */
    cursor: pointer;
}

.social-icons button:hover {
    background-color: var(--gray-600); /* Equivalent to hover:bg-gray-600 */
}

.social-icons svg {
    width: 20px;
    height: 20px;
    /* SVG fill colors are set inline in HTML */
}

/* Sign Up / Sign In Link at the bottom */
.auth-switch-link {
    margin-top: 24px;
    text-align: center;
    font-size: 16px;
    color: var(--gray-400); /* Equivalent to text-gray-400 */
}

.auth-switch-link button {
    margin-left: 8px;
    color: var(--tech-grey); /* Equivalent to text-tech-grey */
    text-decoration: none;
    font-weight: 500;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    transition: color 0.2s, underline 0.2s;
}

.auth-switch-link button:hover {
    color: white; /* Equivalent to hover:text-white */
    text-decoration: underline;
}

/* Ensure only the active form is displayed (handled by JS and .auth-form.active) */
.auth-form:not(.active) {
    display: none;
}


/* Responsive Adjustments */
@media (max-width: 768px) {
    .techlearn-logo {
        top: 16px;
        left: 16px;
    }

    .techlearn-logo img {
        height: 30px;
    }

    .techlearn-logo span {
        font-size: 18px;
    }

    .auth-card {
        padding: 24px;
    }

    .background-code .code-element-1 { font-size: 40px; }
    .background-code .code-element-2 { font-size: 30px; }
    .background-code .code-element-3 { font-size: 40px; }
    .background-code .code-element-4 { font-size: 20px; }
    .background-code .code-element-5 { font-size: 30px; }
     .background-code .code-element-6 { font-size: 20px; }
}

/* Container Styles */
.container {
    max-width: none;
    width: 100%;
    padding: 0;
    margin: 0;
}

/* Navbar Styles */
.navbar {
    background-color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-brand img {
    height: 40px;
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-links a.active {
    background-color: var(--primary-color);
    color: white;
}

.nav-profile {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-profile img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
}

/* Auth Container Styles */
.auth-container {
    max-width: 400px;
    margin: auto;
    background: #1e293b;
    padding: 3rem;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    text-align: center;
}

.auth-container h2 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

.auth-container p {
    color: #94a3b8;
    margin-bottom: 30px;
    font-size: 1rem;
}

.tabs {
    display: none;
}

#login-form {
    display: block;
}

.form-group {
    margin-bottom: 20px;
    position: relative;
    text-align: left;
}

.form-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 1.1rem;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 1px solid #334155;
    border-radius: 8px;
    font-size: 1rem;
    background-color: #334155;
    color: white;
    outline: none;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

.forgot-password {
    display: block;
    text-align: right;
    margin-top: -10px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.forgot-password a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s;
}

.forgot-password a:hover {
    color: #6ea8f7;
    text-decoration: underline;
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    width: 100%;
    font-size: 1.1rem;
    font-weight: bold;
    transition: background-color 0.3s, transform 0.1s;
    margin-top: 10px;
}

.btn-primary:hover {
    background-color: #6366f1;
}

.btn-primary:active {
    transform: scale(0.98);
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 30px 0;
    color: #64748b;
}

.separator::before,
.separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #334155;
    margin: 0 10px;
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.social-icons a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border: 1px solid #334155;
    border-radius: 50%;
    color: #94a3b8;
    font-size: 1.2rem;
    text-decoration: none;
    transition: border-color 0.3s, color 0.3s, background-color 0.3s;
}

.social-icons a:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.signup-link {
    font-size: 1rem;
    color: #94a3b8;
}

.signup-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s;
}

#signup-form {
}

#signup-form h2 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.8rem;
}

#signup-form .form-group input,
#signup-form .form-group select {
}

#signup-form .btn-primary {
}

/* Dashboard Styles */
.dashboard-container {
    padding: 2rem;
}

.profile-card {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-header {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.profile-header img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.stat {
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--primary-color);
}

/* Exercises Grid */
.exercises-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.exercise-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease-in-out;
}

.exercise-card:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
}

/* Compiler Styles */
.compiler-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
    height: calc(100vh - 70px);
}

.exercise-info {
    grid-column: 1 / -1;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.editor-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.editor-tabs {
    display: flex;
    background: var(--secondary-color);
}

.editor-tabs .tab-btn {
    background: transparent;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
}

.editor-tabs .tab-btn.active {
    background: var(--primary-color);
}

.editor {
    display: none;
    height: 400px;
}

.editor.active {
    display: block;
}

.CodeMirror {
    height: 100%;
}

.preview-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
}

.preview-container {
    height: 400px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

#preview-frame {
    width: 100%;
    height: 100%;
    border: none;
}

.action-buttons {
    grid-column: 1 / -1;
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Footer Styles */
.footer {
    background: var(--secondary-color);
    color: white;
    padding: 2rem 0;
    margin-top: 2rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 0 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
}

.footer-section a {
    color: white;
    text-decoration: none;
    display: block;
    margin-bottom: 0.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    font-size: 1.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .compiler-container {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    .nav-links {
        display: none;
    }
}

/* Dashboard specific animations */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Exercise card hover effects */
.exercise-card {
    transition: all 0.3s ease-in-out;
}

.exercise-card:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
}

/* Progress bar animation */
#overallProgressBar div {
    transition: width 0.5s ease-in-out;
}

/* Stat icon box hover effect */
.stat-icon-box {
    transition: all 0.3s ease-in-out;
}

.stat-icon-box:hover {
    transform: scale(1.1);
}

/* Profile section hover effect */
.profile-section {
    transition: all 0.3s ease-in-out;
}

.profile-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1e293b;
}

::-webkit-scrollbar-thumb {
    background: #334155;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #475569;
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Card hover effects */
.hover-card {
    transition: all 0.3s ease;
}

.hover-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Progress bar animation */
@keyframes progress {
    0% {
        width: 0;
    }
    100% {
        width: var(--progress);
    }
}

.progress-bar {
    animation: progress 1s ease-out forwards;
}

/* Timer circle animation */
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.timer-circle {
    animation: rotate 60s linear infinite;
}

/* Badge animations */
.badge {
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.05);
}

/* Button hover effects */
.btn-hover {
    transition: all 0.2s ease;
}

.btn-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Loading skeleton animation */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.skeleton {
    background: linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Custom focus styles */
.focus-ring {
    transition: all 0.2s ease;
}

.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Custom selection color */
::selection {
    background: rgba(59, 130, 246, 0.3);
    color: white;
}

/* Custom tooltip styles */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 4px 8px;
    background: #1e293b;
    color: white;
    font-size: 12px;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-4px);
} 


* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: #0f172a;
    color: #ffffff;
    min-height: 100vh;
    scroll-behavior: smooth;
}

/* Background Pattern */
.bg-pattern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: radial-gradient(ellipse at top, rgba(59, 130, 246, 0.2) 0%, #0f172a 50%, #0f172a 100%);
}

.bg-dots {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23374151' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Header Styles */
.header {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(51, 65, 85, 0.5);
    position: sticky;
    top: 0;
    z-index: 50;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.logo {
    background: white;
    color: #0f172a;
    padding: 8px 16px;
    border-radius: 16px;
    font-weight: bold;
    font-size: 18px;
}

.nav {
    display: flex;
    gap: 8px;
}

.nav-item {
    padding: 8px 24px;
    border-radius: 9999px;
    font-size: 14px;
    font-weight: 500;
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.2s;
}

.nav-item.active {
    background: #334155;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.nav-item:hover:not(.active) {
    background: rgba(51, 65, 85, 0.5);
    color: white;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.icon-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.icon-btn:hover {
    background: rgba(51, 65, 85, 0.5);
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(to bottom right, #2563eb, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.avatar:hover {
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Main Content */
.main {
    position: relative;
    padding: 32px 24px;
}

.welcome-section {
    margin-bottom: 32px;
    animation: fadeIn 0.6s ease-in-out;
}

.welcome-title {
    font-size: 2.25rem;
    font-weight: bold;
    margin-bottom: 8px;
}

.tech-name {
    color: #3b82f6;
}

.stats-row {
    display: flex;
    gap: 24px;
    margin-top: 16px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-indicator {
    width: 48px;
    height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    position: relative;
}

.stat-indicator.interviews {
    background: #334155;
}

.stat-indicator.hired {
    background: #f59e0b;
}

.stat-indicator.project {
    background: #374151;
}

.stat-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin: 4px;
}

.interviews .stat-dot {
    background: #10b981;
}

.hired .stat-dot {
    background: white;
}

.project .stat-dot {
    background: #6b7280;
    margin-left: auto;
}

.stat-text {
    color: #94a3b8;
    font-size: 14px;
}

/* Grid Layout */
.top-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 24px;
    margin-bottom: 32px;
}

.progress-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
}

/* Card Styles */
.card {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.5);
    backdrop-filter: blur(4px);
    border-radius: 24px;
    padding: 24px;
}

.profile-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.profile-avatar {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    background: linear-gradient(to bottom right, #2563eb, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
    border: 4px solid rgba(59, 130, 246, 0.2);
}

.profile-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 4px;
}

.profile-role {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 12px;
}

.salary-badge {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
    padding: 4px 16px;
    border-radius: 12px;
    font-size: 14px;
}

/* Progress Cards */
.card-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 16px;
}

.card-title {
    font-weight: 600;
    flex: 1;
}

.card-icon {
    width: 20px;
    height: 20px;
    color: #3b82f6;
}

.progress-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 4px;
}

.progress-label {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 16px;
}

.chart-bars {
    display: flex;
    align-items: flex-end;
    gap: 4px;
    height: 64px;
    margin-bottom: 8px;
}

.chart-bar {
    width: 8px;
    background: #374151;
    border-radius: 4px 4px 0 0;
}

.chart-bar.active {
    background: #f59e0b;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6b7280;
}

/* Timer Card */
.timer-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.timer-circle {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 4px solid #374151;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timer-progress {
    position: absolute;
    top: -4px;
    left: -4px;
    width: 96px;
    height: 96px;
    border-radius: 50%;
    border: 4px solid #f59e0b;
    border-top-color: transparent;
    transform: rotate(45deg);
}

.timer-content {
    text-align: center;
}

.timer-time {
    font-size: 20px;
    font-weight: bold;
}

.timer-label {
    font-size: 12px;
    color: #94a3b8;
}

.timer-controls {
    display: flex;
    justify-content: center;
}

.play-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #334155;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.play-btn:hover {
    background: #475569;
}

/* Onboarding Card */
.task-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
}

.task-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-check {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-check.completed {
    background: #10b981;
    color: white;
}

.task-check.pending {
    border: 2px solid #6b7280;
}

.task-text {
    font-size: 14px;
}

.task-text.completed {
    color: white;
}

.task-text.pending {
    color: #94a3b8;
}

.onboarding-progress {
    text-align: right;
}

.progress-badge {
    background: #334155;
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 14px;
}

/* Stats Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 32px;
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 8px;
}

.stat-label {
    color: #94a3b8;
    font-size: 14px;
}

/* Exercises Section */
.exercises-section {
    margin-bottom: 32px;
}

.section-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.exercises-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

.exercise-card {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(51, 65, 85, 0.5);
    backdrop-filter: blur(4px);
    border-radius: 24px;
    padding: 24px;
    transition: all 0.3s;
    cursor: pointer;
}

.exercise-card:hover {
    background: rgba(15, 23, 42, 0.7);
    transform: scale(1.02);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
}

.exercise-card.completed {
    border-color: rgba(16, 185, 129, 0.3);
}

.exercise-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.tech-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    border: 1px solid;
}

.tech-html {
    background: rgba(251, 146, 60, 0.1);
    color: #fb923c;
    border-color: rgba(251, 146, 60, 0.2);
}

.tech-css {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    border-color: rgba(59, 130, 246, 0.2);
}

.tech-js {
    background: rgba(234, 179, 8, 0.1);
    color: #eab308;
    border-color: rgba(234, 179, 8, 0.2);
}

.tech-fullstack {
    background: rgba(168, 85, 247, 0.1);
    color: #a855f7;
    border-color: rgba(168, 85, 247, 0.2);
}

.difficulty-beginner {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border-color: rgba(16, 185, 129, 0.2);
}

.difficulty-intermediate {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border-color: rgba(245, 158, 11, 0.2);
}

.difficulty-advanced {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-color: rgba(239, 68, 68, 0.2);
}

.completed-badge {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    margin-bottom: 8px;
    display: inline-block;
}

.exercise-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    line-height: 1.4;
}

.exercise-description {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 16px;
    line-height: 1.5;
}

.exercise-btn {
    width: 100%;
    padding: 12px;
    border-radius: 24px;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}

.exercise-btn.start {
    background: #2563eb;
    color: white;
    box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

.exercise-btn.start:hover {
    background: #1d4ed8;
    transform: scale(1.02);
}

.exercise-btn.review {
    background: #059669;
    color: white;
    box-shadow: 0 4px 6px rgba(5, 150, 105, 0.2);
}

.exercise-btn.review:hover {
    background: #047857;
    transform: scale(1.02);
}

/* Calendar Section */
.calendar-section {
    margin-bottom: 32px;
}

.calendar-header {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 24px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

.calendar-day {
    text-align: center;
}

.day-label {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 8px;
}

.day-number {
    font-weight: 500;
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.event-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    background: rgba(51, 65, 85, 0.5);
    border-radius: 16px;
}

.event-info h4 {
    font-weight: 500;
    margin-bottom: 4px;
}

.event-info p {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 2px;
}

.event-info small {
    color: #6b7280;
    font-size: 12px;
}

.event-avatars {
    display: flex;
    gap: -8px;
}

.event-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #0f172a;
    margin-left: -8px;
}

.event-avatar:first-child {
    margin-left: 0;
}

.avatar-blue {
    background: #3b82f6;
}

.avatar-green {
    background: #10b981;
}

.avatar-yellow {
    background: #f59e0b;
}

.avatar-purple {
    background: #8b5cf6;
}

.avatar-red {
    background: #ef4444;
}

/* Footer */
.footer {
    background: #070054;
    color: white;
    padding: 48px 24px 24px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 32px;
    margin-bottom: 24px;
}

.footer-brand {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
}

.footer-logo {
    width: 40px;
    height: 40px;
    background: white;
    margin-right: 12px;
}

.footer-title {
    font-weight: bold;
    font-size: 20px;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 24px;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-link {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: background 0.2s;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.3);
}

.footer-section h4 {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 16px;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.2s;
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    padding-top: 24px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

.footer-bottom-links {
    display: flex;
    gap: 24px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.2s;
}

.footer-bottom-links a:hover {
    color: white;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        display: none;
    }

    .top-grid {
        grid-template-columns: 1fr;
    }

    .progress-grid {
        grid-template-columns: 1fr;
    }

    .stats-section {
        grid-template-columns: 1fr;
    }

    .exercises-grid {
        grid-template-columns: 1fr;
    }

    .calendar-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-bottom {
        text-align: center;
        flex-direction: column;
    }

    .stats-row {
        flex-direction: column;
        gap: 12px;
    }
} 