// Exercise data
const exercises = [
    {
        id: 1,
        title: "HTML Basics: Creating Your First Webpage",
        description: "Learn the fundamental HTML tags and structure to build a simple webpage from scratch.",
        difficulty: 'Beginner',
        completed: true,
        technology: 'HTML'
    },
    {
        id: 2,
        title: "CSS Styling: Making It Beautiful",
        description: "Apply CSS styles to transform your HTML into a visually appealing webpage.",
        difficulty: 'Beginner',
        completed: true,
        technology: 'CSS'
    },
    {
        id: 3,
        title: "CSS Flexbox Layout",
        description: "Master flexbox to create responsive and flexible layouts for modern web design.",
        difficulty: 'Intermediate',
        completed: true,
        technology: 'CSS'
    },
    {
        id: 4,
        title: "JavaScript Fundamentals",
        description: "Get started with JavaScript variables, functions, and basic DOM manipulation.",
        difficulty: 'Beginner',
        completed: false,
        technology: 'JavaScript'
    },
    {
        id: 5,
        title: "Interactive Forms with JavaScript",
        description: "Build dynamic forms with validation and interactive user feedback.",
        difficulty: 'Intermediate',
        completed: false,
        technology: 'JavaScript'
    },
    {
        id: 6,
        title: "CSS Grid System",
        description: "Learn CSS Grid to create complex, two-dimensional layouts with ease.",
        difficulty: 'Intermediate',
        completed: false,
        technology: 'CSS'
    },
    {
        id: 7,
        title: "DOM Manipulation & Events",
        description: "Advanced JavaScript techniques for dynamic content and user interactions.",
        difficulty: 'Advanced',
        completed: false,
        technology: 'JavaScript'
    },
    {
        id: 8,
        title: "Responsive Web Design",
        description: "Create websites that work perfectly on all devices using responsive design principles.",
        difficulty: 'Intermediate',
        completed: false,
        technology: 'CSS'
    },
    {
        id: 9,
        title: "API Integration with JavaScript",
        description: "Learn to fetch and display data from external APIs using modern JavaScript.",
        difficulty: 'Advanced',
        completed: false,
        technology: 'JavaScript'
    },
    {
        id: 10,
        title: "Final Project: Portfolio Website",
        description: "Combine all your skills to build a complete portfolio website showcasing your work.",
        difficulty: 'Advanced',
        completed: false,
        technology: 'Full Stack'
    }
];

// Helper functions
function getTechnologyClass(tech) {
    switch (tech) {
        case 'HTML': return 'tech-html';
        case 'CSS': return 'tech-css';
        case 'JavaScript': return 'tech-js';
        case 'Full Stack': return 'tech-fullstack';
        default: return 'tech-html';
    }
}

function getDifficultyClass(difficulty) {
    switch (difficulty) {
        case 'Beginner': return 'difficulty-beginner';
        case 'Intermediate': return 'difficulty-intermediate';
        case 'Advanced': return 'difficulty-advanced';
        default: return 'difficulty-beginner';
    }
}

// Render exercises
function renderExercises() {
    const container = document.getElementById('exercises-grid');
    container.innerHTML = exercises.map((exercise, index) => `
        <div class="exercise-card ${exercise.completed ? 'completed' : ''}"
             style="animation-delay: ${index * 100}ms;"
             onclick="startExercise(${exercise.id})">
            <div class="exercise-header">
                <div class="tech-badge ${getTechnologyClass(exercise.technology)}">
                    ${exercise.technology}
                </div>
                <div class="tech-badge ${getDifficultyClass(exercise.difficulty)}">
                    ${exercise.difficulty}
                </div>
            </div>

            ${exercise.completed ? '<div class="completed-badge">✓ Completed</div>' : ''}

            <h3 class="exercise-title">${exercise.title}</h3>
            <p class="exercise-description">${exercise.description}</p>

            <button class="exercise-btn ${exercise.completed ? 'review' : 'start'}">
                ${exercise.completed ? 'Review' : 'Start'}
            </button>
        </div>
    `).join('');
}

// Timer functionality
let timerRunning = false;
let timerSeconds = 155; // 02:35 in seconds

function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

function toggleTimer() {
    const timerBtn = document.getElementById('timer-btn');
    const playIcon = document.getElementById('play-icon');
    const pauseIcon = document.getElementById('pause-icon');

    timerRunning = !timerRunning;

    if (timerRunning) {
        playIcon.style.display = 'none';
        pauseIcon.style.display = 'block';
        startTimer();
    } else {
        playIcon.style.display = 'block';
        pauseIcon.style.display = 'none';
        stopTimer();
    }
}

let timerInterval;

function startTimer() {
    timerInterval = setInterval(() => {
        timerSeconds++;
        document.getElementById('timer-display').textContent = formatTime(timerSeconds);
    }, 1000);
}

function stopTimer() {
    clearInterval(timerInterval);
}

function startExercise(exerciseId) {
    console.log('Starting exercise:', exerciseId);
    alert(`Starting exercise ${exerciseId}. This would normally navigate to the compiler.`);
}

// Navigation functionality with smooth scroll
function setupNavigation() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');

            // Smooth scroll to relevant sections
            const navText = this.textContent.toLowerCase();
            let targetSection = null;

            switch(navText) {
                case 'dashboard':
                    targetSection = document.getElementById('dashboard');
                    break;
                case 'progress':
                    targetSection = document.getElementById('people'); // Stats section for progress
                    break;
                case 'exercises':
                    targetSection = document.getElementById('hiring'); // Exercises section
                    break;
                case 'resources':
                    targetSection = document.getElementById('calendar'); // Calendar/resources section
                    break;
                default:
                    targetSection = document.getElementById('dashboard');
            }

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Add smooth scroll for all internal links
function setupSmoothScroll() {
    // Ensure smooth scrolling for any anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Profile dropdown functionality
function setupProfileDropdown() {
    const profileBtn = document.getElementById('profile-btn');
    const profileDropdown = document.getElementById('profile-dropdown');
    const logoutBtn = document.getElementById('logout-btn');

    if (profileBtn && profileDropdown && logoutBtn) {
        // Toggle dropdown when profile is clicked
        profileBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            profileDropdown.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!profileBtn.contains(e.target) && !profileDropdown.contains(e.target)) {
                profileDropdown.classList.remove('show');
            }
        });

        // Logout functionality
        logoutBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to logout?')) {
                // Redirect to authentication page
                window.location.href = 'index.html';
            }
        });
    }
}

// Typing effect for welcome message
function typeWelcomeMessage() {
    const typingElement = document.getElementById('typing-text');
    const welcomeText = 'Welcome in, ';
    const userName = 'John';

    if (typingElement) {
        let currentIndex = 0;
        typingElement.innerHTML = '';

        // Add the typing cursor
        typingElement.style.borderRight = '3px solid #3b82f6';

        function typeCharacter() {
            if (currentIndex < welcomeText.length) {
                // Type the welcome text normally
                typingElement.innerHTML += welcomeText[currentIndex];
                currentIndex++;
                setTimeout(typeCharacter, 100);
            } else if (currentIndex < welcomeText.length + userName.length) {
                // Type the user name with blue styling
                const nameIndex = currentIndex - welcomeText.length;
                if (nameIndex === 0) {
                    // Start the user name span
                    typingElement.innerHTML += '<span class="user-name">';
                }

                typingElement.innerHTML += userName[nameIndex];

                if (nameIndex === userName.length - 1) {
                    // Close the user name span
                    typingElement.innerHTML += '</span>';
                }

                currentIndex++;
                setTimeout(typeCharacter, 100);
            } else {
                // Remove the cursor after typing is complete
                setTimeout(() => {
                    typingElement.style.borderRight = 'none';
                }, 500);
            }
        }

        // Start typing after a short delay
        setTimeout(typeCharacter, 500);
    }
}

// Dynamic time-based greeting
function setTimeBasedGreeting() {
    const timeGreeting = document.getElementById('time-greeting');
    const currentHour = new Date().getHours();

    let greeting;
    if (currentHour < 12) {
        greeting = 'Good Morning! ☀️';
    } else if (currentHour < 17) {
        greeting = 'Good Afternoon! 🌤️';
    } else {
        greeting = 'Good Evening! 🌙';
    }

    if (timeGreeting) {
        timeGreeting.textContent = greeting;
        // Trigger the fade-in animation
        setTimeout(() => {
            timeGreeting.style.opacity = '1';
        }, 2700);
    }
}

// Initialize the application
function init() {
    renderExercises();
    setupNavigation();
    setupSmoothScroll();
    setupProfileDropdown();
    typeWelcomeMessage();
    setTimeBasedGreeting();

    // Timer button event (if exists)
    const timerBtn = document.getElementById('timer-btn');
    if (timerBtn) {
        timerBtn.addEventListener('click', toggleTimer);
    }

    // Add fade-in animation to elements
    const animatedElements = document.querySelectorAll('.card, .exercise-card');
    animatedElements.forEach((el, index) => {
        el.classList.add('animate-fade-in');
        el.style.animationDelay = `${index * 50}ms`;
    });
}

// Start the application when DOM is loaded
document.addEventListener('DOMContentLoaded', init);