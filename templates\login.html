{% extends 'app.html' %}
{% block content %}

<div class="container">
  <h2>Login form</h2>

    {% with messages = get_flashed_messages() %}
    {% if messages %}
        <div class="alert alert-danger">
            <ul>
                {% for message in messages %}
                <li>{{ message }}</li>
                {% endfor %}
            </ul>
        </div>
    {% endif %}
    {% endwith %}


  <form action="/login" method="POST">
    {{ form.hidden_tag() }}
    <div class="form-group">
        {{ form.email.label(for="email")}}
        {{ form.email(id="email", class="form-control")}}
  
        {% if form.email.errors %}
              <ul>
                  {% for error in form.email.errors %}
                      <li>{{ error }}</li>
                  {% endfor %}
              </ul>
        {% endif %}
    </div>

    <div class="form-group">
        {{ form.password.label(for="password")}}
        {{ form.password(id="password", class="form-control")}}

        {% if form.password.errors %}
                <ul>
                    {% for error in form.password.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
        {% endif %}
    </div>
    {{ form.submit(class="btn btn-dark mt-4")}}
    <a href="/signup" class="btn btn-secondary mt-4">Sign Up Now</a>
  </form>
</div>

{% endblock %}