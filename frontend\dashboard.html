<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechLearn Dashboard</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Add Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Add Roboto Mono for the background code text -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/dashboard.css">
    <script>
        tailwind.config = {
          theme: {
            extend: {
              colors: {
                'tech-blue': '#070054',
                'tech-grey': '#d9d9d9',
                 'gray-800': '#1f2937',
                 'gray-900': '#111827',
              },
               animation: {
                   'fade-in': 'fadeIn 0.5s ease-out forwards',
                    'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
               }
            }
          }
        }
      </script>
</head>
<body>
    <!-- Background -->
    <div class="bg-pattern"></div>
    <div class="bg-dots"></div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">TechLearn Solutions</div>

                <nav class="nav">
                    <button class="nav-item active">Dashboard</button>
                    <button class="nav-item">Progress</button>
                    <button class="nav-item">Exercises</button>
                    <button class="nav-item">Resources</button>
                </nav>

                <div class="header-right">
                    <!-- Settings with text -->
                    <button class="settings-btn" title="Settings">
                        <i class="fas fa-cog"></i>
                        <span>Setting</span>
                    </button>

                    <!-- Notifications -->
                    <button class="icon-btn-single" title="Notifications">
                        <i class="fas fa-bell"></i>
                    </button>

                    <!-- User Profile with dropdown -->
                    <div class="profile-container">
                        <div class="avatar" title="User Profile" id="profile-btn">
                            <i class="fas fa-user"></i>
                        </div>
                        <!-- Logout dropdown -->
                        <div class="profile-dropdown" id="profile-dropdown">
                            <button class="logout-btn" id="logout-btn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Welcome Section -->
            <div id="dashboard" class="welcome-section">
                <div class="welcome-greeting">
                    <h1 class="welcome-title">
                        <span id="typing-text"></span><span class="static-emoji"> 👋</span>
                    </h1>
                    <p class="time-greeting" id="time-greeting">Good Morning!</p>
                </div>

                <div class="welcome-stats">
                    <div class="stat-chip" style="animation-delay: 0.2s;">
                        <span class="stat-icon">✅</span>
                        <span class="stat-text">4/10 Exercises Completed</span>
                    </div>
                    <div class="stat-chip" style="animation-delay: 0.4s;">
                        <span class="stat-icon">🕒</span>
                        <span class="stat-text">Last Active: 2 hours ago</span>
                    </div>
                    <div class="stat-chip" style="animation-delay: 0.6s;">
                        <span class="stat-icon">🧠</span>
                        <span class="stat-text">Level: Intermediate</span>
                    </div>
                </div>
            </div>

            <!-- Top Grid -->
            <div class="top-grid">
                <!-- Profile Card -->
                <div class="card profile-card">
                    <div class="profile-avatar">JD</div>
                    <h3 class="profile-name">John Doe</h3>
                    <p class="profile-role">Frontend Developer</p>
                    <div class="profile-badges">
                        <div class="skill-badge">JavaScript</div>
                        <div class="skill-badge">React</div>
                        <div class="skill-badge">CSS</div>
                    </div>
                </div>

                <!-- Progress Cards -->
                <div class="progress-grid">
                    <!-- Exercise Progress -->
                    <div class="card">
                        <div class="card-header">
                            <span class="card-title">Exercise Progress</span>
                            <svg class="card-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z"/>
                            </svg>
                        </div>
                        <div class="progress-value">4 of 10</div>
                        <div class="progress-label">Exercises Completed</div>
                        <div class="exercise-progress-bar">
                            <div class="progress-fill" style="width: 40%"></div>
                        </div>
                        <div class="exercise-breakdown">
                            <div class="breakdown-item completed">
                                <span class="breakdown-icon">✅</span>
                                <span class="breakdown-text">Beginner: 3/3</span>
                            </div>
                            <div class="breakdown-item partial">
                                <span class="breakdown-icon">✅</span>
                                <span class="breakdown-text">Intermediate: 1/4</span>
                            </div>
                            <div class="breakdown-item pending">
                                <span class="breakdown-icon">⏳</span>
                                <span class="breakdown-text">Advanced: 0/3</span>
                            </div>
                        </div>
                    </div>

                    <!-- Continue Learning -->
                    <div class="card">
                        <div class="card-header">
                            <span class="card-title">Continue Learning</span>
                            <svg class="card-icon" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="continue-learning-content">
                            <div class="current-exercise">
                                <h4 class="exercise-title">HTML Forms & Validation</h4>
                                <div class="exercise-status incomplete">
                                    <span class="status-icon">⏸️</span>
                                    <span class="status-text">Incomplete</span>
                                </div>
                            </div>
                            <button class="resume-btn pulse-btn">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M8 5v14l11-7z"/>
                                </svg>
                                Resume Exercise
                            </button>
                        </div>
                    </div>

                    <!-- Learning Goals -->
                    <div class="card">
                        <div class="card-header">
                            <span class="card-title">Learning Goals</span>
                            <span style="color: #f59e0b; font-weight: bold;">30%</span>
                        </div>
                        <div class="goals-list">
                            <div class="goal-item">
                                <div class="goal-check completed">✓</div>
                                <span class="goal-text completed">Complete 3 Beginner Exercises</span>
                            </div>
                            <div class="goal-item">
                                <div class="goal-check partial">⏳</div>
                                <span class="goal-text partial">Start Intermediate Exercise 1</span>
                            </div>
                            <div class="goal-item">
                                <div class="goal-check pending"></div>
                                <span class="goal-text pending">Customize your profile</span>
                            </div>
                        </div>
                        <div class="goals-progress">
                            <div class="goals-progress-text">30% of Goals Met</div>
                            <div class="goals-progress-bar">
                                <div class="goals-progress-fill" style="width: 30%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Section -->
            <div id="people" class="stats-section">
                <div>
                    <div class="stat-number">78</div>
                    <div class="stat-label">Employe</div>
                </div>
                <div>
                    <div class="stat-number">56</div>
                    <div class="stat-label">Hirings</div>
                </div>
                <div>
                    <div class="stat-number">203</div>
                    <div class="stat-label">Projects</div>
                </div>
            </div>

            <!-- Exercises Section -->
            <div id="hiring" class="exercises-section">
                <h2 class="section-title">
                    <svg width="32" height="32" fill="currentColor" viewBox="0 0 24 24" style="color: #3b82f6;">
                        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                    </svg>
                    Frontend Development Exercises
                </h2>
                <div class="exercises-grid" id="exercises-grid">
                    <!-- Exercises will be populated by JavaScript -->
                </div>
            </div>

            <!-- Calendar Section -->
            <div id="calendar" class="card calendar-section">
                <h3 class="calendar-header">September 2024</h3>
                <div class="calendar-grid">
                    <div class="calendar-day">
                        <div class="day-label">Mon</div>
                        <div class="day-number">22</div>
                    </div>
                    <div class="calendar-day">
                        <div class="day-label">Tue</div>
                        <div class="day-number">22</div>
                    </div>
                    <div class="calendar-day">
                        <div class="day-label">Wed</div>
                        <div class="day-number">24</div>
                    </div>
                    <div class="calendar-day">
                        <div class="day-label">Thu</div>
                        <div class="day-number">25</div>
                    </div>
                    <div class="calendar-day">
                        <div class="day-label">Fri</div>
                        <div class="day-number">22</div>
                    </div>
                    <div class="calendar-day">
                        <div class="day-label">Sat</div>
                        <div class="day-number">22</div>
                    </div>
                    <div class="calendar-day">
                        <div class="day-label">Sun</div>
                        <div class="day-number">22</div>
                    </div>
                </div>
                <div class="events-list">
                    <div class="event-item">
                        <div class="event-info">
                            <h4>8:00 am</h4>
                            <p>Weekly Team Sync</p>
                            <small>Discuss progress on projects</small>
                        </div>
                        <div class="event-avatars">
                            <div class="event-avatar avatar-blue"></div>
                            <div class="event-avatar avatar-green"></div>
                            <div class="event-avatar avatar-yellow"></div>
                        </div>
                    </div>
                    <div class="event-item">
                        <div class="event-info">
                            <h4>11:00 am</h4>
                            <p>Onboarding Session</p>
                            <small>Introduction for new hires</small>
                        </div>
                        <div class="event-avatars">
                            <div class="event-avatar avatar-purple"></div>
                            <div class="event-avatar avatar-red"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <div class="footer-brand">
                        <div class="footer-logo"></div>
                        <h3 class="footer-title">TechLearn Solutions</h3>
                    </div>
                    <p class="footer-description">
                        Empowering students and professionals with the skills and knowledge needed to excel in the technology industry through expert-led training programs.
                    </p>
                    <div class="social-links">
                        <a href="#" class="social-link">f</a>
                        <a href="#" class="social-link">t</a>
                        <a href="#" class="social-link">i</a>
                        <a href="#" class="social-link">in</a>
                    </div>
                </div>

                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#home">Home</a></li>
                        <li><a href="#techprep">TechPrep</a></li>
                        <li><a href="#designlab">DesignLab</a></li>
                        <li><a href="#summer-intern">Summer Internship</a></li>
                        <li><a href="#mini-projects">Mini Projects</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h4>Contact Us</h4>
                    <ul class="footer-links">
                        <li>📧 <EMAIL></li>
                        <li>📞 +91 98765 43210</li>
                        <li>📍 TechLearn Campus, Bangalore, India - 560001</li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>© 2024 TechLearn Solutions. All rights reserved.</p>
                <div class="footer-bottom-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                    <a href="#">Cookie Policy</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="scripts/dashboard.js"></script>
</body>
</html>