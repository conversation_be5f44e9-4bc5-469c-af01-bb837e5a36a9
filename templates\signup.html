{% extends 'app.html' %}
{% block content %}

<div class="container">
  <h2>Sign Up</h2>
  <form action="/signup" method="POST">
    {{ form.hidden_tag() }}
    
    <!-- Name Field -->
    <div class="form-group">
      {{ form.name.label(for="name")}}
      {{ form.name(id="name", class="form-control")}}
      {% if form.name.errors %}
        <ul class="text-danger">
          {% for error in form.name.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- Email Field -->
    <div class="form-group">
      {{ form.email.label(for="email")}}
      {{ form.email(id="email", class="form-control")}}
      {% if form.email.errors %}
        <ul class="text-danger">
          {% for error in form.email.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- Mobile Field -->
    <div class="form-group">
      {{ form.mobile.label(for="mobile")}}
      {{ form.mobile(id="mobile", class="form-control", placeholder="9876543210")}}
      {% if form.mobile.errors %}
        <ul class="text-danger">
          {% for error in form.mobile.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- Gender Field -->
    <div class="form-group">
      {{ form.gender.label(for="gender")}}
      {{ form.gender(id="gender", class="form-control")}}
      {% if form.gender.errors %}
        <ul class="text-danger">
          {% for error in form.gender.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- GitHub Field -->  <!-- ADDED THIS SECTION -->
    <div class="form-group">
      {{ form.github.label(for="github")}}
      {{ form.github(id="github", class="form-control", placeholder="https://github.com/username")}}
      {% if form.github.errors %}
        <ul class="text-danger">
          {% for error in form.github.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- Password Field -->
    <div class="form-group">
      {{ form.password.label(for="password")}}
      {{ form.password(id="password", class="form-control")}}
      {% if form.password.errors %}
        <ul class="text-danger">
          {% for error in form.password.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- Confirm Password Field -->
    <div class="form-group">
      {{ form.confirm_password.label(for="confirm_password")}}
      {{ form.confirm_password(id="confirm_password", class="form-control")}}
      {% if form.confirm_password.errors %}
        <ul class="text-danger">
          {% for error in form.confirm_password.errors %}
            <li>{{ error }}</li>
          {% endfor %}
        </ul>
      {% endif %}
    </div>

    <!-- Submit Button -->
    <div class="form-group mt-4">
      {{ form.submit(class="btn btn-dark")}}
      <a href="/login" class="btn btn-secondary">Login Instead</a>
    </div>
  </form>
</div>

{% endblock %}