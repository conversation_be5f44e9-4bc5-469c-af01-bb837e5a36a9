// Global variables
let editor;
let currentExercise = null;

// Initialize the page
document.addEventListener('DOMContentLoaded', async () => {
    // Check if user is logged in
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user) {
        window.location.href = 'index.html';
        return;
    }

    // Get exercise ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const exerciseId = urlParams.get('exercise');
    
    if (!exerciseId) {
        alert('No exercise selected. Redirecting to dashboard...');
        window.location.href = 'dashboard.html';
        return;
    }

    // Initialize Monaco Editor
    require.config({ paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.36.1/min/vs' }});
    require(['vs/editor/editor.main'], function() {
        editor = monaco.editor.create(document.getElementById('editor'), {
            value: '// Your code will appear here...',
            language: 'javascript',
            theme: 'vs-dark',
            automaticLayout: true,
            minimap: {
                enabled: false
            }
        });
    });

    // Load exercise data
    await loadExercise(exerciseId);

    // Set up event listeners
    setupEventListeners();
});

// Load exercise data from the backend
async function loadExercise(exerciseId) {
    try {
        const response = await fetch(`http://localhost:5000/exercises/${exerciseId}`, {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
        });

        if (!response.ok) {
            throw new Error('Failed to load exercise');
        }

        currentExercise = await response.json();
        displayExercise(currentExercise);
    } catch (error) {
        console.error('Error loading exercise:', error);
        alert('Failed to load exercise. Please try again later.');
        window.location.href = 'dashboard.html';
    }
}

// Display exercise information
function displayExercise(exercise) {
    document.getElementById('exerciseTitle').textContent = exercise.title;
    document.getElementById('exerciseDescription').innerHTML = exercise.description;
    
    // Set initial code in editor
    if (editor) {
        editor.setValue(exercise.starterCode || '// Write your code here...');
    }
}

// Set up event listeners
function setupEventListeners() {
    // Run button
    document.getElementById('runBtn').addEventListener('click', runCode);
    
    // Reset button
    document.getElementById('resetBtn').addEventListener('click', resetCode);
    
    // Back button
    document.getElementById('backBtn').addEventListener('click', () => {
        window.location.href = 'dashboard.html';
    });
}

// Run the code
function runCode() {
    const code = editor.getValue();
    const previewFrame = document.getElementById('previewFrame');
    const consoleOutput = document.getElementById('console');
    
    try {
        // Clear previous console output
        consoleOutput.innerHTML = '';
        
        // Create a new console.log function that writes to our console output
        const customConsole = {
            log: (...args) => {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : arg
                ).join(' ');
                consoleOutput.innerHTML += `<div class="text-green-400">${message}</div>`;
            },
            error: (...args) => {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : arg
                ).join(' ');
                consoleOutput.innerHTML += `<div class="text-red-400">${message}</div>`;
            }
        };

        // Create a sandboxed environment for the code
        const sandbox = {
            console: customConsole,
            document: previewFrame.contentDocument,
            window: previewFrame.contentWindow
        };

        // Execute the code in the sandbox
        const wrappedCode = `
            try {
                ${code}
            } catch (error) {
                console.error(error.message);
            }
        `;
        
        new Function('console', 'document', 'window', wrappedCode)(
            sandbox.console,
            sandbox.document,
            sandbox.window
        );
    } catch (error) {
        consoleOutput.innerHTML += `<div class="text-red-400">Error: ${error.message}</div>`;
    }
}

// Reset the code to the starter code
function resetCode() {
    if (currentExercise && editor) {
        editor.setValue(currentExercise.starterCode || '// Write your code here...');
        document.getElementById('previewFrame').contentDocument.body.innerHTML = '';
        document.getElementById('console').innerHTML = '<div class="text-gray-400">Console output will appear here...</div>';
    }
} 