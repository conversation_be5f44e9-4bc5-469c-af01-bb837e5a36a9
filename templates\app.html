<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="TechLearn Solutions - Your premier destination for technology education and learning solutions">
    <title>TechLearn Solutions</title>
    <!-- CSS Links -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        .navbar {
            background: linear-gradient(45deg, #83b9ff, #0066ff);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px 0;
            margin-top: auto; /* For sticky footer */
        }
        .card {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .brand-logo {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            object-fit: contain;
        }
        html, body {
            height: 100%;
        }
        .wrapper {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
    </style>
</head>
<body class="d-flex flex-column min-vh-100">
    <div class="wrapper">
        <!-- Navigation Bar -->
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="{{ url_for('login') }}">
                    <img src="{{ url_for('static', filename='logo.png') }}" 
                         alt="TechLearn Solutions Logo" 
                         class="brand-logo">
                    TechLearn Solutions
                </a>
                <button class="navbar-toggler" type="button" 
                        data-toggle="collapse" data-target="#navbarNav"
                        aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ml-auto">
                        {% if 'user_id' in session %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" 
                                   id="navbarDropdown" role="button" 
                                   data-toggle="dropdown"
                                   aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-user"></i> 
                                    <span class="sr-only">User menu for</span>
                                    {{ session.get('user_name', 'User') }}
                                </a>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item" href="{{ url_for('dashboard') }}">
                                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item text-danger" href="{{ url_for('logout') }}">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </a>
                                </div>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('signup') }}">Sign Up</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-grow-1">
            <div class="container mt-4">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} 
                                        alert-dismissible fade show"
                                 role="alert">
                                {{ message }}
                                <button type="button" class="close" 
                                        data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer mt-auto">
            <div class="container text-center">
                <div class="social-links mb-3">
                    <a href="https://github.com/your-org" 
                       class="text-dark mx-3"
                       aria-label="GitHub profile">
                        <i class="fab fa-github"></i>
                    </a>
                    <a href="https://linkedin.com/company/your-org" 
                       class="text-dark mx-3"
                       aria-label="LinkedIn profile">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="https://twitter.com/your-org" 
                       class="text-dark mx-3"
                       aria-label="Twitter profile">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>
                <p class="mb-0">&copy; {{ current_year }} TechLearn Solutions. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.min.js"></script>
</body>
</html>